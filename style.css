nav a.active-page { font-weight: bold !important; }
@import url('https://fonts.googleapis.com/css2?family=Biondi+Sans:wght@300&display=swap');

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Biondi Sans', 'Helvetica', sans-serif;
}

body {
    background: #ffffff !important;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Unified Header */
header {
    background: #fafafa;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    position: fixed;
    width: 100%;
    top: 0;
    z-index: 1000;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 0;
}

.logo-container {
    display: flex;
    flex-direction: column;
}

.logo {
    font-size: 28px;
    color: #333;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.logo span:first-child {
    font-weight: 700; /* BOLD for <PERSON><PERSON><PERSON> */
}

.logo span:last-child {
    font-weight: 300; /* Regular for Capitanio */
}

.subtitle {
    font-size: 14px;
    color: #666;
    margin-top: 5px;
    font-weight: 300;
}

nav ul {
    display: flex;
    gap: 30px;
    list-style: none;
}
nav a {
    text-decoration: none;
    color: #333;
    font-weight: 300;
    display: block; /* Make links block-level */
    padding: 10px 15px; /* Add some padding for better touch experience */
}


nav a.active-page {
    font-weight: bold;
}

/* Hide menu icon on desktop */
.menu-icon {
    display: none;
}

/* Hero Section */
.hero {
    margin-top: 160px;
    padding: 80px 0;
    background: #ffffff;
}

.hero-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 50px;
    align-items: center;
}

.hero-text p {
    font-size: 18px;
    line-height: 1.6;
    color: #666;
    margin-bottom: 30px;
    font-weight: 300;
}

.review-link {
    display: inline-block;
    background: #4285f4;
    color: white;
    padding: 12px 24px;
    border-radius: 30px;
    text-decoration: none;
    margin-top: 20px;
    transition: transform 0.3s ease;
    font-weight: 300;
}

/* Features Section */
.features {
    padding: 80px 0;
    background: #f9f9f9;
}

.feature-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
    margin-top: 40px;
}

.feature-card {
    background: #f5f5f5;
    padding: 30px;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.05);
}

.feature-card h3 {
    font-weight: 300;
}

/* Contact Section */
.contact {
    background: #f8f9fa;
    padding: 80px 0;
}

.contact-info {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
    margin-top: 40px;
}

.contact-info h3 {
    font-weight: 300;
}

/* Space Section */
.space-section {
    padding: 180px 0 80px;
}

.space-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 50px;
    align-items: center;
}

.space-text h1 {
    font-size: 36px;
    margin-bottom: 20px;
    text-transform: uppercase;
    font-weight: 300;
}

.space-text p {
    margin: 15px 0;
    line-height: 1.6;
    font-weight: 300;
}

.space-image img {
    width: 100%;
    border-radius: 10px;
}

/* Testimonial Section */
.testimonial {
    background: #f8f9fa;
    padding: 60px 0;
    text-align: center;
}

.testimonial h2 {
    font-weight: 300;
}

.testimonial p {
    font-weight: 300;
}

/* Clients Section */
.clients-section {
    padding: 180px 0 80px;
    text-align: center;
}

.clients-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 50px;
    align-items: center;
}

.clients-text h1 {
    font-size: 36px;
    margin-bottom: 20px;
    text-transform: uppercase;
    font-weight: 300;
}

.clients-text p {
    margin: 15px 0;
    line-height: 1.6;
    font-weight: 300;
}

.clients-image img {
    width: 100%;
    border-radius: 10px;
}

/* Responsive Design */
@media (max-width: 768px) {

    /* Hamburger Menu Icon */
    .menu-icon {
        display: block;
        cursor: pointer;
        position: absolute;
        top: 20px;
        right: 20px;
        z-index: 1001;
    }

    .menu-icon .bar {
        width: 30px;
        height: 3px;
        background-color: #333;
        margin: 6px 0;
        transition: 0.4s;
        border-radius: 2px;
    }

    /* Hamburger animation */
    .menu-icon.active .bar:nth-child(1) {
        transform: rotate(-45deg) translate(-9px, 6px);
    }

    .menu-icon.active .bar:nth-child(2) {
        opacity: 0;
    }

    .menu-icon.active .bar:nth-child(3) {
        transform: rotate(45deg) translate(-8px, -8px);
    }

    nav ul {
        position: absolute;
        top: 80px;
        left: 0;
        width: 100%;
        background-color: #fafafa;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        padding: 20px;
        text-align: center;
        z-index: 1000;
        flex-direction: column;
        gap: 15px;
        max-height: 0;
        overflow: hidden;
        transition: max-height 0.3s ease-in-out;
    }

    nav ul.active {
        display: flex;
        max-height: 400px; /* Adjust based on content */
    }

    nav ul li {
        margin: 0;
    }

    nav ul li a {
        padding: 15px 20px;
        display: block;
        border-radius: 5px;
        transition: background-color 0.3s ease;
        min-height: 44px; /* Ensure touch targets are at least 44px */
        display: flex;
        align-items: center;
        justify-content: center;
    }

    nav ul li a:hover,
    nav ul li a:active {
        background-color: #e9e9e9;
    }

    .hero-content {
        grid-template-columns: 1fr; /* Stack text and image */
    }

    .hero-text {
        order: 2; /* Place text below image */
    }

    .hero-image {
        order: 1; /* Place image above text */
    }

    .hero-text p {
        font-size: 16px; /* Adjust font size */
    }

    .clients-content {
        grid-template-columns: 1fr;
    }

    .contact-content {
        grid-template-columns: 1fr;
    }

    .space-content {
        grid-template-columns: 1fr;
    }

    .clients-section {
        padding-top: 120px;
    }

    .contact-section {
        padding-top: 120px;
    }

    .space-section {
        padding-top: 120px;
    }
}

/* Tablet screens */
@media (max-width: 1024px) and (min-width: 769px) {
    .container {
        padding: 0 30px;
    }

    .hero-content,
    .contact-content,
    .clients-content,
    .space-content {
        gap: 30px;
    }

    .logo-container img {
        width: 180px !important;
    }
}

/* Small screens */
@media (max-width: 480px) {
    .container {
        padding: 0 15px; /* Reduce container padding */
    }

    .header-content {
        padding: 15px 0; /* Reduce header padding */
    }

    .logo-container img {
        width: 150px !important; /* Make logo smaller on very small screens */
    }

    .hero {
        padding: 60px 0; /* Reduce hero padding */
    }

    .hero-text h1 {
        font-size: 32px; /* Reduce hero title size */
    }

    .hero-text p {
        font-size: 15px; /* Further adjust font size */
    }

    .contact-text h1,
    .clients-text h1,
    .space-text h1 {
        font-size: 28px; /* Reduce heading sizes on small screens */
    }

    .contact-text p,
    .clients-text p,
    .space-text p {
        font-size: 16px; /* Reduce paragraph font size */
    }

    .menu-icon {
        top: 15px;
        right: 15px;
    }

    nav ul {
        top: 70px; /* Adjust menu position for smaller header */
    }
}

.contact-section {
    padding: 180px 0 80px;
}

.contact-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 50px;
    align-items: center;
}

.contact-text h1 {
    font-size: 36px;
    margin-bottom: 20px;
    color: #333;
    text-transform: uppercase;
    font-weight: 300;
}

.contact-text p {
    margin: 15px 0;
    font-size: 18px;
    font-weight: 300;
}

.contact-details {
    margin-top: 30px;
}

.contact-details p {
    margin: 10px 0;
    font-weight: 300;
}

.contact-image img {
    width: 100%;
    border-radius: 10px;
}