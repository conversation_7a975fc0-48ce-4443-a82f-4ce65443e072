document.addEventListener('DOMContentLoaded', function() {
    const menuIcon = document.querySelector('.menu-icon');
    const navList = document.querySelector('nav ul');

    if (menuIcon && navList) {
        // Toggle mobile menu
        menuIcon.addEventListener('click', function() {
            navList.classList.toggle('active');
            menuIcon.classList.toggle('active');
        });

        // Close menu when clicking on a link
        const navLinks = navList.querySelectorAll('a');
        navLinks.forEach(link => {
            link.addEventListener('click', function() {
                navList.classList.remove('active');
                menuIcon.classList.remove('active');
            });
        });

        // Close menu when clicking outside
        document.addEventListener('click', function(event) {
            if (!menuIcon.contains(event.target) && !navList.contains(event.target)) {
                navList.classList.remove('active');
                menuIcon.classList.remove('active');
            }
        });

        // Close menu on window resize if it's open
        window.addEventListener('resize', function() {
            if (window.innerWidth > 768) {
                navList.classList.remove('active');
                menuIcon.classList.remove('active');
            }
        });
    }
});